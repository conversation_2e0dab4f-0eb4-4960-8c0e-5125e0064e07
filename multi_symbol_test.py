#!/usr/bin/env python3
"""
多品种回测脚本
支持同时回测多个交易品种并生成汇总报告

使用方法:
    uv run multi_symbol_test.py
"""

from bt2.mysql import DBManager
from bt2.context import DBTaskStatusContext
from bt2.redismanager import (
    MultiKLineRedisManager,
    DynParamRedisManager,
    PlatIndRedisManager,
    RiskRedisManager,
    RunControlRedisManager,
    MultiOpenIntRedisManager,
)
from bt2.sizers import NoneSize
from bt2.ws import MultiKLineWSManager, BinanceBrokerWSManager
from bt2.api import BinanceBrokerAPIManager
from bt2.storages import IndCsvManager
from bt2.cerebro import Cerebro2
from bt2.brokers import BackBroker2
from bt2.datafeeds import OneWSRedisDataFeed
from bt2.datafeed import ResampleDataFeed
from bt2.strategies import *
from bt2.analyzers import MultiSymbolAnalyzer
import backtrader as bt


# =============================================================================
# 配置区域 - 在这里修改回测参数
# =============================================================================

# 要回测的品种列表
SYMBOLS = ["btcusdt", "ethusdt", "bnbusdt", "solusdt", "xrpusdt"]

# 使用的策略
STRATEGY = YugeStrategy

# 初始资金（每个品种）
INITIAL_CASH = 1000000

# 回测时间范围
START_DATE = "2025-08-01"
END_DATE = "2025-09-01"

# 是否为模拟回测
SIMULATE = True

# 输出目录 (只保存HTML格式报告)
OUTPUT_DIR = "multi_symbol_reports"

# =============================================================================


def run_single_symbol(
    symbol, strategy, stra_params, context_params_template, initial_cash, managers
):
    """
    运行单个品种的回测
    Args:
        symbol: 品种名称
        strategy: 策略类
        stra_params: 策略参数
        context_params_template: 数据参数模板
        initial_cash: 初始资金
        managers: 管理器字典
    Returns:
        tuple: (策略实例, cerebro实例) 或 None（如果失败）
    """
    try:
        print(f"\n开始回测 {symbol}...")

        # 获取策略默认参数
        context_params = strategy.get_default_data_params(symbol=symbol)

        # 应用模板参数
        for i, d in enumerate(context_params):
            if i < len(context_params_template):
                d.update(context_params_template[i])

        # 创建cerebro
        cerebro = Cerebro2(
            cheat_on_open=True, stdstats=True, exactbars=0, preload=False
        )

        # 添加分析器
        cerebro.addanalyzer(bt.analyzers.TradeAnalyzer, _name="trades")
        cerebro.addanalyzer(bt.analyzers.SharpeRatio, _name="sharpe")
        cerebro.addanalyzer(bt.analyzers.DrawDown, _name="drawdown")

        # 创建broker
        broker = BackBroker2(cashbalance=initial_cash)
        cerebro.setbroker(broker=broker)

        # 创建数据源
        for data_params in context_params:
            dataname = data_params.pop("dataname")
            resampledata = data_params.pop("resampledata", None)
            if resampledata:
                data = ResampleDataFeed(
                    origin_data=cerebro.datasbyname[resampledata],
                    dataname=dataname,
                    **data_params,
                )
            else:
                data = OneWSRedisDataFeed(dataname=dataname, **data_params)
            cerebro.adddata(data)

        # 添加策略
        cerebro.addstrategy(strategy=strategy, **stra_params)

        # 设置仓位控制
        cerebro.addsizer(NoneSize)

        # 运行回测
        task_id = hash(symbol) % 10000  # 为每个品种生成唯一task_id
        with DBTaskStatusContext(task_id=task_id, conn_db=False):
            results = cerebro.run()

        strategy_instance = results[0]
        print(f"✅ {symbol} 回测完成，最终价值: {cerebro.broker.getvalue():,.2f}")

        return strategy_instance, cerebro

    except Exception as e:
        print(f"❌ {symbol} 回测失败: {e}")
        return None


def multi_symbol_backtest():
    """多品种回测主函数"""

    # 初始化管理器
    # 手动配置mysql数据库
    taskmanager = DBManager(uri="mysql+pymysql://lq:123@localhost/quant")

    # 手动配置数据源redis
    klinelivemanager = MultiKLineRedisManager(
        host="*************",
        port=6379,
        db=13,
        password="fhj666888",
    )

    # 手动配置持仓量redis
    openintmanager = MultiOpenIntRedisManager(
        host="*************",
        port=6379,
        db=13,
        password="fhj666888",
    )

    # 手动配置历史数据websocket
    wsmanager = MultiKLineWSManager(
        uri="ws://apiws.karlet.com/market",
    )

    # 手动配置币安账户api
    brokerapimanager = BinanceBrokerAPIManager(
        uri="http://api.karlet.com",
    )

    # 手动配置币安账户websocket
    brokermanager = BinanceBrokerWSManager(uri="ws://*************:8080/ws")

    # 手动配置动态参数redis
    dynparammanager = DynParamRedisManager(
        host="127.0.0.1",
        port=6379,
        db=12,
        password="fhj666888",
    )

    # 手动配置运行控制redis
    controlmanager = RunControlRedisManager(
        host="127.0.0.1",
        port=6379,
        db=12,
        password="fhj666888",
    )

    # 手动配置指标csv存储
    indcsvmanager = IndCsvManager(filedir="/home/<USER>/workspace/bt2/logs/indcsv")

    # 手动配置平台指标
    platindmanager = PlatIndRedisManager(
        host="*************",
        port=6379,
        db=13,
        password="fhj666888",
    )

    # 手动配置风控
    riskmanager = RiskRedisManager(
        host="127.0.0.1",
        port=6379,
        db=13,
        password="fhj666888",
    )

    managers = {
        "taskmanager": taskmanager,
        "klinelivemanager": klinelivemanager,
        "openintmanager": openintmanager,
        "wsmanager": wsmanager,
        "brokermanager": brokermanager,
        "brokerapimanager": brokerapimanager,
        "dynparammanager": dynparammanager,
        "indcsvmanager": indcsvmanager,
        "riskmanager": riskmanager,
        "platindmanager": platindmanager,
        "controlmanager": controlmanager,
    }

    with (
        taskmanager,
        klinelivemanager,
        openintmanager,
        wsmanager,
        brokermanager,
        brokerapimanager,
        dynparammanager,
        indcsvmanager,
        riskmanager,
        platindmanager,
        controlmanager,
    ):
        try:
            # 使用配置常量
            symbols = SYMBOLS
            strategy = STRATEGY
            simulate = SIMULATE
            initial_cash = INITIAL_CASH

            # 策略参数
            stra_params = {
                "simulate": simulate,
            }

            # 数据参数模板（应用到所有品种）
            context_params_template = [
                {
                    "is_live": not simulate,
                    "start_date": START_DATE,
                    "end_date": END_DATE,
                }
            ]

            # 创建多品种分析器
            multi_analyzer = MultiSymbolAnalyzer(output_dir=OUTPUT_DIR)

            print("=" * 80)
            print("开始多品种回测")
            print("=" * 80)
            print(f"回测品种: {', '.join(symbols)}")
            print(f"使用策略: {strategy.__name__}")
            print(f"回测模式: {'模拟回测' if simulate else '实盘回测'}")
            print(f"初始资金: {initial_cash:,} (每个品种)")

            # 逐个运行品种回测
            successful_results = {}
            failed_symbols = []

            for symbol in symbols:
                result = run_single_symbol(
                    symbol=symbol,
                    strategy=strategy,
                    stra_params=stra_params,
                    context_params_template=context_params_template,
                    initial_cash=initial_cash,
                    managers=managers,
                )

                if result is not None:
                    strategy_instance, cerebro = result
                    successful_results[symbol] = (strategy_instance, cerebro)

                    # 添加到多品种分析器
                    multi_analyzer.add_symbol_result(symbol, strategy_instance, cerebro)
                else:
                    failed_symbols.append(symbol)

            # 生成汇总报告
            print("\n" + "=" * 80)
            print("生成汇总报告")
            print("=" * 80)

            if successful_results:
                # 打印汇总信息
                multi_analyzer.print_summary()

                # 保存报告文件
                print(f"\n保存报告文件...")
                saved_files = multi_analyzer.save_summary_report(
                    strategy_name=strategy.__name__
                )

                print(f"\n报告已保存到以下文件:")
                for file_path in saved_files:
                    # 转换为字符串以支持 endswith 方法
                    file_str = str(file_path)
                    if file_str.endswith(".csv"):
                        print(f"  📊 {file_str} (数据汇总)")
                    elif file_str.endswith(".html"):
                        print(f"  📄 {file_str}")
                    else:
                        print(f"  - {file_str}")

                # 生成详细的比较分析
                summary_report = multi_analyzer.generate_summary_report()

                print(f"\n详细性能比较:")
                print("-" * 60)
                performance_comparison = summary_report["performance_comparison"]

                for metric in ["total_return_pct", "sharpe_ratio"]:
                    if metric in performance_comparison:
                        stats = performance_comparison[metric]["_stats"]
                        print(f"{metric}:")
                        print(f"  最佳: {stats['best_symbol']} ({stats['max']:.2f})")
                        print(f"  最差: {stats['worst_symbol']} ({stats['min']:.2f})")
                        print(f"  平均: {stats['mean']:.2f}")

                print(f"\n风险指标比较:")
                print("-" * 60)
                risk_comparison = summary_report["risk_comparison"]

                if "max_drawdown_pct" in risk_comparison:
                    stats = risk_comparison["max_drawdown_pct"]["_stats"]
                    print(f"最大回撤:")
                    print(
                        f"  最低风险: {stats['lowest_risk_symbol']} ({stats['min']:.2f}%)"
                    )
                    print(
                        f"  最高风险: {stats['highest_risk_symbol']} ({stats['max']:.2f}%)"
                    )
                    print(f"  平均: {stats['mean']:.2f}%")

                # 显示组合分析
                portfolio_analysis = summary_report["portfolio_analysis"]
                if "equal_weight_portfolio" in portfolio_analysis:
                    portfolio = portfolio_analysis["equal_weight_portfolio"]
                    print(f"\n等权重组合表现:")
                    print("-" * 60)
                    print(f"平均收益率: {portfolio['average_return_pct']:.2f}%")
                    print(f"平均夏普比率: {portfolio['average_sharpe_ratio']:.3f}")
                    print(f"最大回撤: {portfolio['max_drawdown_pct']:.2f}%")

            else:
                print("❌ 所有品种回测都失败了")

            # 显示失败的品种
            if failed_symbols:
                print(f"\n失败的品种: {', '.join(failed_symbols)}")

            print(f"\n多品种回测完成！")
            print(f"成功: {len(successful_results)}/{len(symbols)} 个品种")

            return successful_results, multi_analyzer

        except Exception as e:
            print(f"多品种回测出错: {e}")
            import traceback

            traceback.print_exc()
            return None, None


def save_charts_for_all_symbols(results):
    """为所有品种生成并保存bokeh图表"""
    import os
    from datetime import datetime
    from bokeh.plotting import output_file
    from bokeh.io import save

    # 创建图表保存目录
    charts_dir = os.path.join(OUTPUT_DIR, "charts")
    os.makedirs(charts_dir, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    for symbol, (strategy, cerebro) in results.items():
        try:
            print(f"   📊 生成 {symbol.upper()} 图表...")

            # 生成图表文件名
            chart_filename = f"{STRATEGY.__name__}_{symbol}_{timestamp}.html"
            chart_path = os.path.join(charts_dir, chart_filename)

            # 设置输出文件
            output_file(chart_path)

            # 生成bokeh图表
            from bt2.plot import BokehStrategyPlotter

            plotter = BokehStrategyPlotter()
            plots = plotter.plot(strategy)

            # 保存图表到文件
            save(plots)

            print(f"   ✅ {symbol.upper()} 图表已保存: {chart_path}")

        except Exception as e:
            print(f"   ❌ {symbol.upper()} 图表生成失败: {e}")

    print(f"\n📁 所有图表已保存到: {charts_dir}/")


def analyze_individual_results(results):
    """分析单个品种的详细结果"""
    print("\n" + "=" * 80)
    print("单个品种详细分析")
    print("=" * 80)

    for symbol, (strategy, cerebro) in results.items():
        print(f"\n{symbol.upper()} 详细分析:")
        print("-" * 40)

        # 生成单个品种报告
        report = strategy.report()

        # 显示关键指标
        basic = report["basic_info"]
        perf = report["performance_metrics"]
        trade = report["trade_analysis"]
        risk = report["risk_metrics"]

        print(f"初始资金: {basic['initial_cash']:,.2f}")
        print(f"最终价值: {basic['final_value']:,.2f}")
        print(f"总收益率: {perf.get('total_return_pct', 0):.2f}%")
        print(f"总交易次数: {trade['total_trades']}")
        print(f"胜率: {trade['win_rate_pct']:.2f}%")
        print(f"盈利因子: {trade['profit_factor']:.2f}")
        print(f"夏普比率: {perf.get('sharpe_ratio', 0):.3f}")
        print(f"最大回撤: {risk.get('max_drawdown_pct', 0):.2f}%")


if __name__ == "__main__":
    print("🚀 PyTrader 多品种回测系统")
    print("=" * 60)
    print("📋 配置信息:")
    print(f"   品种: {', '.join(SYMBOLS)}")
    print(f"   策略: {STRATEGY.__name__}")
    print(f"   时间: {START_DATE} 至 {END_DATE}")
    print(f"   资金: {INITIAL_CASH:,} (每个品种)")
    print(f"   模式: {'模拟回测' if SIMULATE else '实盘回测'}")
    print("=" * 60)

    # 运行多品种回测
    results, multi_analyzer = multi_symbol_backtest()

    if results:
        # 分析单个品种结果
        analyze_individual_results(results)

        # 为每个品种生成并保存图表
        if results:
            print(f"\n📈 生成品种图表...")
            save_charts_for_all_symbols(results)

        print(f"\n🎉 多品种回测完成！")
        print(f"✅ 成功: {len(results)}/{len(SYMBOLS)} 个品种")
        print(f"📁 报告保存在: {OUTPUT_DIR}/ 目录")
        print(f"📊 图表保存在: {OUTPUT_DIR}/charts/ 目录")
    else:
        print("❌ 所有品种回测都失败了")

    print("\n" + "=" * 60)
    print("💡 使用提示:")
    print("   - 修改文件顶部的配置常量来调整参数")
    print("   - 运行命令: uv run multi_symbol_test.py")
    print("   - 查看HTML报告: ls multi_symbol_reports/*.html")
    print("   - 查看数据汇总: ls multi_symbol_reports/*DataSummary*.csv")
    print("   - 查看图表: ls multi_symbol_reports/charts/")
    print("   - 报告格式: HTML格式 + CSV数据汇总")
    print("=" * 60)
