__all__ = [
    "YugeStrategy",
]


import backtrader as bt
from ...indicators import *
from ...conditions import *
from ...strategy import *
from ...storages import *
from ..live import LiveEntryExitStratgegy
from logging import getLogger

logger = getLogger(__name__)


class YugeStrategy(LiveEntryExitStratgegy):
    """
    宇哥设计策略
    """

    alias = ("YugeStrategy",)

    params = (
        (
            "open_size",
            9,
        ),
    )

    @classmethod
    def get_default_data_params(cls, symbol):
        params = [
            {
                "dataname": f"{symbol}_1m",
                "platform": "binance",
                "inst_type": "futures",
                "freq": "1m",
                "symbol": symbol,
                # "days_ago": 5,
                # "is_live": False,
            },
        ]
        return params

    @classmethod
    def get_default_account_name(cls):
        """默认账户"""
        return "liquid_test"

    def init(self):
        avgPrice = AvgPriceInd()
        entry = self.add_entry_cond(YugeEntryCond)
        self.add_exit_cond(YugeStopLossExitCond, self.datas[0], entry)
        self.add_exit_cond(YugeStopProfitExitCond, self.datas[0], entry, avgPrice)

    def stop(self):
        logger.info("自动停止")

    def start(self):
        logger.info("任务开始")

    def notify_order(self, order):
        self.logger.info("order: %s", order)

    def preheartbeat(self):
        logger.info("preheatbeat dt: %s", bt.num2date(self.data.datetime[0]))


class YugeEntryCond(EntryCond):

    alias = ("YugeEntryCond",)

    params = (
        (
            "open_size",
            9,
        ),
    )

    def __init__(self):
        # 60分钟图均线大于24线开多小于24线开空
        self.sma24 = bt.indicators.SMA(self.data, period=24)
        self.sma24_plot = BPlot(self.sma24, b_subplot=False)
        self.sma24_plot.plotlines.value.b_color = "green"
        self.sma60 = bt.indicators.SMA(self.data, period=60)
        self.sma60_plot = BPlot(self.sma60, b_subplot=False)
        self.sma60_plot.plotlines.value.b_color = "red"
        maCond = Cmp(self.sma60, self.sma24, b_plot=True)

        # 60分钟MACD处于由0轴之下做空0轴之上做多
        self.macd = bt.indicators.MACD(
            self.data, period_me1=12, period_me2=26, period_signal=9
        )
        self.macdCond = CmpValue(self.macd.macd, num=0)

        # 连续5根1分钟持续放量的K线 依托第5根K线的低点阳线做多 第五根阴线高点做空
        self.trendDirection = If(Eq(maCond, self.macdCond), maCond, ConstValue(num=0))
        self.volInc = Increase(Lag(self.data.volume), period=3)
        isBull = BullishBar(self.data)

        bullCond = GteValue(AccumulateInd(isBull, period=5), num=5)
        bearCond = GteValue(AccumulateInd(Not(isBull), period=5), num=5)

        self.amp = AmplitudeRate(self.data, period=1)
        self.ampAcc = AccumulateInd(self.amp, period=5, b_plot=True)
        ampCond = And(LtValue(self.ampAcc, num=2), GtValue(self.ampAcc, num=0.2))

        # 入场条件
        self.longCond = And(
            EqValue(self.trendDirection, num=1),
            self.volInc,
            bullCond,
            ampCond,
        )
        self.shortCond = And(
            EqValue(self.trendDirection, num=-1),
            self.volInc,
            bearCond,
            ampCond,
        )

        self.sma10 = bt.indicators.SMA(self.data, period=10)

    def next(self):
        value = 0
        if self.longCond[0] and self.sma10[0] < self.data.close[0]:
            value = 1
        if self.shortCond[0] and self.sma10[0] > self.data.close[0]:
            value = -1
        return value

    def submit_open_order(self):
        if self.lines.value[0] == 1:
            self._owner.buy(
                size=self.p.open_size,
                side="long",
                priceMatch="OPPONENT_5",
                timeInForce="IOC",
            )
            return True
        if self.lines.value[0] == -1:
            self._owner.sell(
                size=self.p.open_size,
                side="short",
                priceMatch="OPPONENT_5",
                timeInForce="IOC",
            )
            return True
        return False


class YugeStopProfitExitCond(ExitCond):
    alias = ("YugeStopProfitExitCond",)

    params = (
        (
            "open_size",
            9,
        ),
    )

    def __init__(self):
        # 记录开仓价格和止损价格
        self.posSide = Step(self.datas[1])  # 1, -1

        entryPrice = self.datas[2]

        longStopPrice = Step(
            If(EqValue(self.datas[1], num=1), Lag(self.data.low), ConstValue(num=0)),
        )
        shortStopPrice = Step(
            If(EqValue(self.datas[1], num=-1), Lag(self.data.high), ConstValue(num=0)),
        )

        self.sma10 = bt.indicators.SMA(self.data, period=10, b_plot=True)

        stopPrice = If(EqValue(self.posSide, num=1), longStopPrice, shortStopPrice)
        # 盈亏比
        risk = Abs(entryPrice - stopPrice)
        profit = Mul(self.data.close - entryPrice, self.posSide)
        self.profitRatio = Div(profit, risk, b_plot=True)

    def next(self):
        if self.profitRatio[0] >= 2 and self.profitRatio[0] <= 3:
            return True
        if self.profitRatio[0] > 3:
            return (self.posSide == 1 and self.data.high < self.sma10) or (
                self.posSide == -1 and self.data.low > self.sma10
            )

    def submit_close_order(self):
        if self.profitRatio[0] >= 2 and self.profitRatio[0] <= 3:
            size = self.p.open_size / 3
        else:
            size = None

        self._owner.close(
            size=size,
            priceMatch="OPPONENT_5",
            timeInForce="IOC",
            trigger=self.get_name(),
        )
        return True


class YugeStopLossExitCond(ExitCond):
    alias = ("YugeStopLossExitCond",)

    params = (
        (
            "open_size",
            9,
        ),
    )

    def __init__(self):
        self.posSide = Step(self.datas[1])  # 1, -1

        self.longStopPrice = Step(
            If(EqValue(self.datas[1], num=1), Lag(self.data.low), ConstValue(num=0)),
        )
        self.shortStopPrice = Step(
            If(EqValue(self.datas[1], num=-1), Lag(self.data.high), ConstValue(num=0)),
        )
        self.longStopLossCond = Lt(self.data.low, self.longStopPrice)
        self.shortStopLossCond = Gt(self.data.high, self.shortStopPrice)

    def next(self):
        return (self.posSide[0] == 1 and self.longStopLossCond[0]) or (
            self.posSide[0] == -1 and self.shortStopLossCond[0]
        )

    def submit_close_order(self):
        self._owner.close(
            priceMatch="OPPONENT_5",
            timeInForce="IOC",
            trigger=self.get_name(),
        )
        return True
