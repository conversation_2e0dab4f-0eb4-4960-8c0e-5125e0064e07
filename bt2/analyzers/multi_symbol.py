"""
多品种回测分析器
"""

import pandas as pd
import numpy as np
from datetime import datetime
from .report import StrategyReportGenerator
from .output import ReportOutputManager


class MultiSymbolAnalyzer:
    """多品种回测分析器"""

    def __init__(self, output_dir="multi_symbol_reports"):
        """
        初始化多品种分析器
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self.symbol_reports = {}
        self.symbol_strategies = {}

    def add_symbol_result(self, symbol, strategy, cerebro=None):
        """
        添加单个品种的回测结果
        Args:
            symbol: 品种名称
            strategy: 策略实例
            cerebro: cerebro实例（可选）
        """
        # 生成单个品种的报告
        report = strategy.report()

        self.symbol_reports[symbol] = report
        self.symbol_strategies[symbol] = strategy

        print(f"✅ {symbol} 回测完成")

    def generate_summary_report(self):
        """生成整体汇总报告"""
        if not self.symbol_reports:
            return {"error": "没有可用的回测结果"}

        summary_report = {
            "overview": self._generate_overview(),
            "performance_comparison": self._compare_performance(),
            "risk_comparison": self._compare_risk(),
            "trade_comparison": self._compare_trades(),
            "correlation_analysis": self._analyze_correlations(),
            "portfolio_analysis": self._analyze_portfolio(),
            "ranking": self._rank_strategies(),
            "detailed_results": self.symbol_reports,
        }

        return summary_report

    def _generate_overview(self):
        """生成概览信息"""
        total_symbols = len(self.symbol_reports)

        # 统计基本信息
        total_trades = sum(
            report["trade_analysis"]["total_trades"]
            for report in self.symbol_reports.values()
        )

        avg_win_rate = np.mean(
            [
                report["trade_analysis"]["win_rate_pct"]
                for report in self.symbol_reports.values()
            ]
        )

        total_return = sum(
            report["performance_metrics"]["total_pnl"]
            for report in self.symbol_reports.values()
        )

        return {
            "total_symbols": total_symbols,
            "symbols": list(self.symbol_reports.keys()),
            "total_trades": total_trades,
            "average_win_rate": avg_win_rate,
            "total_return": total_return,
            "analysis_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }

    def _compare_performance(self):
        """比较性能指标"""
        comparison = {}

        # 按品种组织数据，每个品种包含其所有性能指标
        for symbol, report in self.symbol_reports.items():
            perf_metrics = report["performance_metrics"]
            comparison[symbol] = {
                "total_return_pct": perf_metrics.get("total_return_pct", 0),
                "sharpe_ratio": perf_metrics.get("sharpe_ratio", 0),
                "sortino_ratio": perf_metrics.get("sortino_ratio", 0),
                "calmar_ratio": perf_metrics.get("calmar_ratio", 0),
                "volatility": perf_metrics.get("volatility", 0),
            }

        return comparison

    def _compare_risk(self):
        """比较风险指标"""
        comparison = {}

        # 按品种组织数据，每个品种包含其所有风险指标
        for symbol, report in self.symbol_reports.items():
            risk_metrics = report["risk_metrics"]
            comparison[symbol] = {
                "max_drawdown_pct": risk_metrics.get("max_drawdown_pct", 0),
                "var_95": risk_metrics.get("var_95", 0),
                "var_99": risk_metrics.get("var_99", 0),
                "volatility": risk_metrics.get("volatility", 0),
            }

        return comparison

    def _compare_trades(self):
        """比较交易指标"""
        comparison = {}

        # 按品种组织数据，每个品种包含其所有交易指标
        for symbol, report in self.symbol_reports.items():
            trade_analysis = report["trade_analysis"]
            comparison[symbol] = {
                "total_trades": trade_analysis.get("total_trades", 0),
                "win_rate_pct": trade_analysis.get("win_rate_pct", 0),
                "profit_factor": trade_analysis.get("profit_factor", 0),
                "avg_win": trade_analysis.get("avg_win", 0),
                "avg_loss": trade_analysis.get("avg_loss", 0),
                "largest_win": trade_analysis.get("largest_win", 0),
                "largest_loss": trade_analysis.get("largest_loss", 0),
            }

        return comparison

    def _analyze_correlations(self):
        """分析品种间相关性"""
        # 这里简化处理，实际应该基于收益率序列计算
        symbols = list(self.symbol_reports.keys())

        if len(symbols) < 2:
            return {"note": "需要至少2个品种才能计算相关性"}

        # 基于收益率的简单相关性分析
        returns = {}
        for symbol, report in self.symbol_reports.items():
            returns[symbol] = report["performance_metrics"].get("total_return_pct", 0)

        correlation_matrix = {}
        for symbol1 in symbols:
            correlation_matrix[symbol1] = {}
            for symbol2 in symbols:
                if symbol1 == symbol2:
                    correlation_matrix[symbol1][symbol2] = 1.0
                else:
                    # 简化的相关性计算（实际应该基于时间序列）
                    correlation_matrix[symbol1][symbol2] = 0.5  # 占位符

        return {
            "correlation_matrix": correlation_matrix,
            "note": "相关性分析需要基于时间序列数据，此处为简化版本",
        }

    def _analyze_portfolio(self):
        """分析组合表现"""
        if not self.symbol_reports:
            return {}

        # 计算等权重组合表现
        total_return = sum(
            report["performance_metrics"]["total_pnl"]
            for report in self.symbol_reports.values()
        )

        avg_return_pct = np.mean(
            [
                report["performance_metrics"]["total_return_pct"]
                for report in self.symbol_reports.values()
            ]
        )

        avg_sharpe = np.mean(
            [
                report["performance_metrics"].get("sharpe_ratio", 0)
                for report in self.symbol_reports.values()
            ]
        )

        max_drawdown = max(
            [
                report["risk_metrics"].get("max_drawdown_pct", 0)
                for report in self.symbol_reports.values()
            ]
        )

        return {
            "equal_weight_portfolio": {
                "total_return": total_return,
                "average_return_pct": avg_return_pct,
                "average_sharpe_ratio": avg_sharpe,
                "max_drawdown_pct": max_drawdown,
                "diversification_benefit": "TODO: 需要基于相关性计算",
            }
        }

    def _rank_strategies(self):
        """对策略进行排名"""
        rankings = {}

        # 按不同指标排名
        ranking_metrics = {
            "total_return_pct": "总收益率",
            "sharpe_ratio": "夏普比率",
            "win_rate_pct": "胜率",
            "profit_factor": "盈利因子",
            "max_drawdown_pct": "最大回撤（越小越好）",
        }

        for metric, description in ranking_metrics.items():
            if metric == "max_drawdown_pct":
                # 回撤越小越好
                sorted_symbols = sorted(
                    self.symbol_reports.keys(),
                    key=lambda x: self.symbol_reports[x]["risk_metrics"].get(
                        metric, float("inf")
                    ),
                )
            else:
                # 其他指标越大越好
                if "risk_metrics" in self.symbol_reports[
                    list(self.symbol_reports.keys())[0]
                ] and metric in ["max_drawdown_pct"]:
                    metric_source = "risk_metrics"
                elif "trade_analysis" in self.symbol_reports[
                    list(self.symbol_reports.keys())[0]
                ] and metric in ["win_rate_pct", "profit_factor"]:
                    metric_source = "trade_analysis"
                else:
                    metric_source = "performance_metrics"

                sorted_symbols = sorted(
                    self.symbol_reports.keys(),
                    key=lambda x: self.symbol_reports[x][metric_source].get(metric, 0),
                    reverse=True,
                )

            rankings[metric] = {
                "description": description,
                "ranking": [
                    (i + 1, symbol, self._get_metric_value(symbol, metric))
                    for i, symbol in enumerate(sorted_symbols)
                ],
            }

        # 综合评分排名
        rankings["composite_score"] = self._calculate_composite_ranking()

        return rankings

    def _get_metric_value(self, symbol, metric):
        """获取指标值"""
        report = self.symbol_reports[symbol]

        if metric in ["max_drawdown_pct", "var_95", "var_99", "volatility"]:
            return report["risk_metrics"].get(metric, 0)
        elif metric in [
            "win_rate_pct",
            "profit_factor",
            "total_trades",
            "avg_win",
            "avg_loss",
        ]:
            return report["trade_analysis"].get(metric, 0)
        else:
            return report["performance_metrics"].get(metric, 0)

    def _calculate_composite_ranking(self):
        """计算综合评分排名"""
        scores = {}

        for symbol in self.symbol_reports.keys():
            report = self.symbol_reports[symbol]

            # 综合评分公式（可以根据需要调整权重）
            return_score = (
                report["performance_metrics"].get("total_return_pct", 0) * 0.3
            )
            sharpe_score = report["performance_metrics"].get("sharpe_ratio", 0) * 0.3
            win_rate_score = report["trade_analysis"].get("win_rate_pct", 0) * 0.2
            drawdown_penalty = report["risk_metrics"].get("max_drawdown_pct", 0) * 0.2

            composite_score = (
                return_score + sharpe_score + win_rate_score - drawdown_penalty
            )
            scores[symbol] = composite_score

        # 排序
        sorted_symbols = sorted(scores.keys(), key=lambda x: scores[x], reverse=True)

        return {
            "description": "综合评分（收益率30% + 夏普比率30% + 胜率20% - 回撤20%）",
            "ranking": [
                (i + 1, symbol, scores[symbol])
                for i, symbol in enumerate(sorted_symbols)
            ],
        }

    def save_summary_report(self, strategy_name="MultiSymbol"):
        """保存汇总报告"""
        summary_report = self.generate_summary_report()

        # 保存汇总报告
        output_manager = ReportOutputManager(output_dir=self.output_dir)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        saved_files = output_manager.save_report(
            report=summary_report,
            strategy_name=f"{strategy_name}_Summary",
            symbol="ALL",
            format="html",
            timestamp=timestamp,
        )

        # 保存各个品种的详细报告
        for symbol, report in self.symbol_reports.items():
            symbol_files = output_manager.save_report(
                report=report,
                strategy_name=strategy_name,
                symbol=symbol,
                format="html",
                timestamp=timestamp,
            )
            saved_files.extend(symbol_files)

        # 保存数据汇总报告 (CSV格式)
        data_summary_files = self._save_data_summary_report(
            strategy_name, timestamp, output_manager
        )
        saved_files.extend(data_summary_files)

        return saved_files

    def print_summary(self):
        """打印汇总信息"""
        summary_report = self.generate_summary_report()

        print("\n" + "=" * 80)
        print("多品种回测汇总报告")
        print("=" * 80)

        overview = summary_report["overview"]
        print(f"回测品种数量: {overview['total_symbols']}")
        print(f"回测品种: {', '.join(overview['symbols'])}")
        print(f"总交易次数: {overview['total_trades']}")
        print(f"平均胜率: {overview['average_win_rate']:.2f}%")
        print(f"总盈亏: {overview['total_return']:,.2f}")

        print(
            f"\n{'品种':<12} {'收益率%':<10} {'夏普比率':<10} {'胜率%':<8} {'最大回撤%':<12}"
        )
        print("-" * 60)

        for symbol in overview["symbols"]:
            report = self.symbol_reports[symbol]
            perf = report["performance_metrics"]
            trade = report["trade_analysis"]
            risk = report["risk_metrics"]

            print(
                f"{symbol:<12} {perf.get('total_return_pct', 0):<10.2f} "
                f"{perf.get('sharpe_ratio', 0):<10.3f} "
                f"{trade.get('win_rate_pct', 0):<8.2f} "
                f"{risk.get('max_drawdown_pct', 0):<12.2f}"
            )

        # 显示排名
        rankings = summary_report["ranking"]
        print(f"\n综合评分排名:")
        print("-" * 40)
        for rank, symbol, score in rankings["composite_score"]["ranking"]:
            print(f"{rank}. {symbol:<12} 评分: {score:.2f}")

    def _save_data_summary_report(self, strategy_name, timestamp, output_manager):
        """保存数据汇总报告 (CSV格式)"""
        import pandas as pd
        import os

        saved_files = []

        try:
            # 创建汇总数据
            summary_data = []

            for symbol in self.symbol_reports.keys():
                if symbol in self.symbol_reports:
                    report = self.symbol_reports[symbol]
                    perf = report["performance_metrics"]
                    trade = report["trade_analysis"]
                    risk = report["risk_metrics"]

                    row_data = {
                        "品种": symbol.upper(),
                        "总收益率(%)": round(perf.get("total_return_pct", 0), 2),
                        "总盈亏": round(perf.get("total_pnl", 0), 2),
                        "夏普比率": round(perf.get("sharpe_ratio", 0), 3),
                        "索提诺比率": round(perf.get("sortino_ratio", 0), 3),
                        "卡玛比率": round(perf.get("calmar_ratio", 0), 3),
                        "最大回撤(%)": round(risk.get("max_drawdown_pct", 0), 2),
                        "年化波动率(%)": round(risk.get("volatility", 0), 2),
                        "VaR_95(%)": round(risk.get("var_95", 0), 2),
                        "VaR_99(%)": round(risk.get("var_99", 0), 2),
                        "总交易次数": trade.get("total_trades", 0),
                        "胜率(%)": round(trade.get("win_rate_pct", 0), 2),
                        "盈利因子": round(trade.get("profit_factor", 0), 2),
                        "平均盈利": round(trade.get("avg_win", 0), 2),
                        "平均亏损": round(trade.get("avg_loss", 0), 2),
                        "最大单笔盈利": round(trade.get("largest_win", 0), 2),
                        "最大单笔亏损": round(trade.get("largest_loss", 0), 2),
                    }
                    summary_data.append(row_data)

            if summary_data:
                # 创建DataFrame
                df = pd.DataFrame(summary_data)

                # 保存CSV文件
                csv_filename = f"{strategy_name}_DataSummary_ALL_{timestamp}.csv"
                csv_path = os.path.join(str(output_manager.output_dir), csv_filename)
                df.to_csv(csv_path, index=False, encoding="utf-8-sig")
                saved_files.append(csv_path)

                print(f"✅ 数据汇总报告已保存: {csv_filename}")

        except Exception as e:
            print(f"❌ 数据汇总报告保存失败: {e}")

        return saved_files
