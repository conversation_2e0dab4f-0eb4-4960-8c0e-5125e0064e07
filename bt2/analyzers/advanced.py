"""
高级统计分析器
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import backtrader as bt
from backtrader.analyzer import Analyzer


class AdvancedTradeAnalyzer(Analyzer):
    """
    高级交易分析器
    提供更详细的交易统计信息
    """

    def __init__(self):
        super().__init__()
        self.trades = []
        self.equity_curve = []
        self.drawdown_periods = []

    def notify_trade(self, trade):
        """交易完成时的回调"""
        if trade.isclosed:
            trade_info = {
                "entry_time": trade.dtopen,
                "exit_time": trade.dtclose,
                "entry_price": trade.price,
                "exit_price": (
                    trade.pnlcomm / trade.size + trade.price
                    if trade.size != 0
                    else trade.price
                ),
                "size": trade.size,
                "pnl": trade.pnl,
                "pnlcomm": trade.pnlcomm,
                "commission": trade.commission,
                "holding_bars": trade.barlen,
                "holding_time": trade.dtclose - trade.dtopen,
            }
            self.trades.append(trade_info)

    def next(self):
        """每个bar的回调"""
        # 记录资金曲线
        portfolio_value = self.strategy.broker.getvalue()
        self.equity_curve.append(
            {
                "datetime": self.strategy.data.datetime[0],
                "value": portfolio_value,
                "cash": self.strategy.broker.getcash(),
                "position_value": portfolio_value - self.strategy.broker.getcash(),
            }
        )

    def get_analysis(self):
        """获取分析结果"""
        if not self.trades:
            return self._empty_analysis()

        return {
            "trades": self._analyze_trades(),
            "performance": self._analyze_performance(),
            "risk": self._analyze_risk(),
            "time": self._analyze_time_patterns(),
            "equity_curve": self.equity_curve,
            "monthly_returns": self._calculate_monthly_returns(),
            "drawdown_analysis": self._analyze_drawdowns(),
        }

    def _empty_analysis(self):
        """空交易时的分析结果"""
        return {
            "trades": {
                "total": 0,
                "winning": 0,
                "losing": 0,
                "win_rate": 0.0,
                "profit_factor": 0.0,
                "avg_win": 0.0,
                "avg_loss": 0.0,
                "largest_win": 0.0,
                "largest_loss": 0.0,
                "total_pnl": 0.0,
            },
            "performance": {},
            "risk": {},
            "time": {},
            "equity_curve": self.equity_curve,
            "monthly_returns": [],
            "drawdown_analysis": {},
        }

    def _analyze_trades(self):
        """分析交易数据"""
        pnls = [trade["pnlcomm"] for trade in self.trades]
        winning_trades = [pnl for pnl in pnls if pnl > 0]
        losing_trades = [pnl for pnl in pnls if pnl < 0]

        total_trades = len(self.trades)
        winning_count = len(winning_trades)
        losing_count = len(losing_trades)

        win_rate = winning_count / total_trades if total_trades > 0 else 0

        gross_profit = sum(winning_trades) if winning_trades else 0
        gross_loss = abs(sum(losing_trades)) if losing_trades else 0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float("inf")

        return {
            "total": total_trades,
            "winning": winning_count,
            "losing": losing_count,
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "avg_win": np.mean(winning_trades) if winning_trades else 0,
            "avg_loss": np.mean(losing_trades) if losing_trades else 0,
            "largest_win": max(pnls) if pnls else 0,
            "largest_loss": min(pnls) if pnls else 0,
            "total_pnl": sum(pnls),
            "gross_profit": gross_profit,
            "gross_loss": gross_loss,
            "avg_trade": np.mean(pnls) if pnls else 0,
            "std_trade": np.std(pnls) if pnls else 0,
            "median_trade": np.median(pnls) if pnls else 0,
            "consecutive_wins": self._calculate_consecutive_wins(),
            "consecutive_losses": self._calculate_consecutive_losses(),
        }

    def _analyze_performance(self):
        """分析性能指标"""
        if not self.equity_curve:
            return {}

        values = [point["value"] for point in self.equity_curve]
        returns = pd.Series(values).pct_change().dropna()

        initial_value = values[0] if values else 0
        final_value = values[-1] if values else 0
        total_return = (
            (final_value - initial_value) / initial_value if initial_value > 0 else 0
        )

        return {
            "initial_value": initial_value,
            "final_value": final_value,
            "total_return": total_return,
            "total_return_pct": total_return * 100,
            "sharpe_ratio": self._calculate_sharpe_ratio(returns),
            "sortino_ratio": self._calculate_sortino_ratio(returns),
            "calmar_ratio": self._calculate_calmar_ratio(returns, values),
            "volatility": returns.std() * np.sqrt(252) if len(returns) > 0 else 0,
            "skewness": returns.skew() if len(returns) > 0 else 0,
            "kurtosis": returns.kurtosis() if len(returns) > 0 else 0,
        }

    def _analyze_risk(self):
        """分析风险指标"""
        if not self.equity_curve:
            return {}

        values = pd.Series([point["value"] for point in self.equity_curve])
        returns = values.pct_change().dropna()

        # 计算回撤
        peak = values.expanding().max()
        drawdown = (peak - values) / peak

        return {
            "max_drawdown": drawdown.max(),
            "max_drawdown_pct": drawdown.max() * 100,
            "avg_drawdown": (
                drawdown[drawdown > 0].mean() if len(drawdown[drawdown > 0]) > 0 else 0
            ),
            "drawdown_duration": self._calculate_max_drawdown_duration(values),
            "var_95": np.percentile(returns, 5) if len(returns) > 0 else 0,
            "var_99": np.percentile(returns, 1) if len(returns) > 0 else 0,
            "cvar_95": (
                returns[returns <= np.percentile(returns, 5)].mean()
                if len(returns) > 0
                else 0
            ),
            "cvar_99": (
                returns[returns <= np.percentile(returns, 1)].mean()
                if len(returns) > 0
                else 0
            ),
            "downside_deviation": self._calculate_downside_deviation(returns),
        }

    def _analyze_time_patterns(self):
        """分析时间模式"""
        if not self.trades:
            return {}

        # 持仓时间分析
        holding_times = [
            trade["holding_time"].total_seconds() / 3600 for trade in self.trades
        ]  # 小时

        # 按月份分析
        monthly_pnl = {}
        for trade in self.trades:
            month = bt.num2date(trade["exit_time"]).month
            if month not in monthly_pnl:
                monthly_pnl[month] = []
            monthly_pnl[month].append(trade["pnlcomm"])

        # 按星期分析
        weekly_pnl = {}
        for trade in self.trades:
            weekday = bt.num2date(trade["exit_time"]).weekday()
            if weekday not in weekly_pnl:
                weekly_pnl[weekday] = []
            weekly_pnl[weekday].append(trade["pnlcomm"])

        return {
            "avg_holding_time_hours": np.mean(holding_times) if holding_times else 0,
            "max_holding_time_hours": max(holding_times) if holding_times else 0,
            "min_holding_time_hours": min(holding_times) if holding_times else 0,
            "median_holding_time_hours": (
                np.median(holding_times) if holding_times else 0
            ),
            "monthly_performance": {
                month: np.sum(pnls) for month, pnls in monthly_pnl.items()
            },
            "weekly_performance": {
                day: np.sum(pnls) for day, pnls in weekly_pnl.items()
            },
        }

    def _calculate_monthly_returns(self):
        """计算月度收益率"""
        if not self.equity_curve:
            return []

        df = pd.DataFrame(self.equity_curve)
        df["datetime"] = pd.to_datetime(df["datetime"].apply(bt.num2date))
        df.set_index("datetime", inplace=True)

        # 按月重采样
        monthly = df["value"].resample("M").last()
        monthly_returns = monthly.pct_change().dropna()

        return [
            {"month": date.strftime("%Y-%m"), "return": ret}
            for date, ret in monthly_returns.items()
        ]

    def _analyze_drawdowns(self):
        """分析回撤期间"""
        if not self.equity_curve:
            return {}

        values = pd.Series([point["value"] for point in self.equity_curve])
        peak = values.expanding().max()
        drawdown = (peak - values) / peak

        # 找到回撤期间
        in_drawdown = drawdown > 0.01  # 1%以上的回撤
        drawdown_periods = []

        start_idx = None
        for i, is_dd in enumerate(in_drawdown):
            if is_dd and start_idx is None:
                start_idx = i
            elif not is_dd and start_idx is not None:
                drawdown_periods.append(
                    {
                        "start": start_idx,
                        "end": i - 1,
                        "duration": i - start_idx,
                        "max_drawdown": drawdown[start_idx:i].max(),
                    }
                )
                start_idx = None

        return {
            "total_drawdown_periods": len(drawdown_periods),
            "avg_drawdown_duration": (
                np.mean([dd["duration"] for dd in drawdown_periods])
                if drawdown_periods
                else 0
            ),
            "max_drawdown_duration": (
                max([dd["duration"] for dd in drawdown_periods])
                if drawdown_periods
                else 0
            ),
            "drawdown_periods": drawdown_periods,
        }

    def _calculate_consecutive_wins(self):
        """计算最大连续盈利次数"""
        if not self.trades:
            return 0

        max_consecutive = 0
        current_consecutive = 0

        for trade in self.trades:
            if trade["pnlcomm"] > 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def _calculate_consecutive_losses(self):
        """计算最大连续亏损次数"""
        if not self.trades:
            return 0

        max_consecutive = 0
        current_consecutive = 0

        for trade in self.trades:
            if trade["pnlcomm"] < 0:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0

        return max_consecutive

    def _calculate_sharpe_ratio(self, returns, risk_free_rate=0.02):
        """计算夏普比率"""
        if len(returns) == 0 or returns.std() == 0:
            return 0.0

        excess_returns = returns - risk_free_rate / 252
        return excess_returns.mean() / returns.std() * np.sqrt(252)

    def _calculate_sortino_ratio(self, returns, risk_free_rate=0.02):
        """计算索提诺比率"""
        if len(returns) == 0:
            return 0.0

        excess_returns = returns - risk_free_rate / 252
        downside_returns = returns[returns < 0]

        if len(downside_returns) == 0:
            return float("inf") if excess_returns.mean() > 0 else 0.0

        downside_deviation = downside_returns.std()
        if downside_deviation == 0:
            return 0.0

        return excess_returns.mean() / downside_deviation * np.sqrt(252)

    def _calculate_calmar_ratio(self, returns, values):
        """计算卡尔玛比率"""
        if len(returns) == 0:
            return 0.0

        annual_return = returns.mean() * 252
        max_drawdown = self._calculate_max_drawdown_value(values)

        if max_drawdown == 0:
            return float("inf") if annual_return > 0 else 0.0

        return annual_return / max_drawdown

    def _calculate_max_drawdown_value(self, values):
        """计算最大回撤值"""
        if len(values) <= 1:
            return 0.0

        peak = values.expanding().max()
        drawdown = (peak - values) / peak
        return drawdown.max()

    def _calculate_max_drawdown_duration(self, values):
        """计算最大回撤持续时间"""
        if len(values) <= 1:
            return 0

        peak = values.expanding().max()
        drawdown = peak - values

        # 找到回撤期间
        in_drawdown = drawdown > 0
        max_duration = 0
        current_duration = 0

        for is_dd in in_drawdown:
            if is_dd:
                current_duration += 1
                max_duration = max(max_duration, current_duration)
            else:
                current_duration = 0

        return max_duration

    def _calculate_downside_deviation(self, returns, target_return=0):
        """计算下行偏差"""
        if len(returns) == 0:
            return 0.0

        downside_returns = returns[returns < target_return]
        if len(downside_returns) == 0:
            return 0.0

        return downside_returns.std() * np.sqrt(252)
