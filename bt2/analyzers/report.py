"""
策略统计报告生成器
"""

import pandas as pd
import numpy as np
from datetime import datetime
import backtrader as bt
from ..trade import EventTrade
from ..order import EventOrder


class StrategyReportGenerator:
    """策略统计报告生成器"""

    def __init__(self, strategy):
        """
        初始化报告生成器
        Args:
            strategy: 策略实例
        """
        self.strategy = strategy
        self.broker = strategy.broker
        self.data = strategy.data

    def generate_report(self):
        """
        生成完整的策略统计报告
        Returns:
            dict: 包含各种统计指标的报告字典
        """
        report = {
            "basic_info": self._get_basic_info(),
            "performance_metrics": self._get_performance_metrics(),
            "trade_analysis": self._get_trade_analysis(),
            "risk_metrics": self._get_risk_metrics(),
            "position_analysis": self._get_position_analysis(),
            "time_analysis": self._get_time_analysis(),
            "detailed_trades": self._get_detailed_trades(),
            "equity_curve": self._get_equity_curve(),
        }

        # 生成报告摘要
        report["summary"] = self._generate_summary(report)

        return report

    def _get_basic_info(self):
        """获取基本信息"""
        return {
            "strategy_name": self.strategy.__class__.__name__,
            "symbol": (
                self.data.get_symbol()
                if hasattr(self.data, "get_symbol")
                else "Unknown"
            ),
            "start_date": self._get_start_date(),
            "end_date": self._get_end_date(),
            "total_bars": len(self.data.datetime.array),
            "initial_cash": getattr(self.broker.p, "cashbalance", 10000),
            "final_value": self.broker.getvalue(),
            "commission_rate": self._get_commission_rate(),
        }

    def _get_performance_metrics(self):
        """获取性能指标"""
        initial_cash = getattr(self.broker.p, "cashbalance", 10000)
        final_value = self.broker.getvalue()

        total_return = (final_value - initial_cash) / initial_cash

        # 获取资金曲线数据
        equity_data = self._get_equity_data()

        metrics = {
            "total_return": total_return,
            "total_return_pct": total_return * 100,
            "total_pnl": final_value - initial_cash,
            "final_value": final_value,
        }

        if equity_data is not None and len(equity_data) > 1:
            # 计算更多性能指标
            returns = equity_data.pct_change().dropna()

            if len(returns) > 0:
                metrics.update(
                    {
                        "sharpe_ratio": self._calculate_sharpe_ratio(returns),
                        "max_drawdown": self._calculate_max_drawdown(equity_data),
                        "max_drawdown_pct": self._calculate_max_drawdown_pct(
                            equity_data
                        ),
                        "volatility": returns.std() * np.sqrt(252),  # 年化波动率
                        "calmar_ratio": self._calculate_calmar_ratio(
                            total_return, equity_data
                        ),
                    }
                )

        return metrics

    def _get_trade_analysis(self):
        """获取交易分析"""
        trades = self._get_closed_trades()

        if not trades:
            return {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "win_rate_pct": 0.0,
                "avg_win": 0.0,
                "avg_loss": 0.0,
                "profit_factor": 0.0,
                "largest_win": 0.0,
                "largest_loss": 0.0,
                "avg_trade": 0.0,
                "total_pnl": 0.0,
                "gross_profit": 0.0,
                "gross_loss": 0.0,
            }

        trade_pnls = [trade.pnl for trade in trades]
        winning_trades = [pnl for pnl in trade_pnls if pnl > 0]
        losing_trades = [pnl for pnl in trade_pnls if pnl < 0]

        total_trades = len(trades)
        num_winning = len(winning_trades)
        num_losing = len(losing_trades)

        win_rate = num_winning / total_trades if total_trades > 0 else 0
        avg_win = np.mean(winning_trades) if winning_trades else 0
        avg_loss = np.mean(losing_trades) if losing_trades else 0

        gross_profit = sum(winning_trades)
        gross_loss = abs(sum(losing_trades))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float("inf")

        return {
            "total_trades": total_trades,
            "winning_trades": num_winning,
            "losing_trades": num_losing,
            "win_rate": win_rate,
            "win_rate_pct": win_rate * 100,
            "avg_win": avg_win,
            "avg_loss": avg_loss,
            "profit_factor": profit_factor,
            "largest_win": max(trade_pnls) if trade_pnls else 0,
            "largest_loss": min(trade_pnls) if trade_pnls else 0,
            "avg_trade": np.mean(trade_pnls) if trade_pnls else 0,
            "total_pnl": sum(trade_pnls),
            "gross_profit": gross_profit,
            "gross_loss": gross_loss,
        }

    def _get_risk_metrics(self):
        """获取风险指标"""
        equity_data = self._get_equity_data()

        if equity_data is None or len(equity_data) <= 1:
            return {
                "max_drawdown": 0.0,
                "max_drawdown_pct": 0.0,
                "var_95": 0.0,
                "var_99": 0.0,
                "volatility": 0.0,
            }

        returns = equity_data.pct_change().dropna()

        return {
            "max_drawdown": self._calculate_max_drawdown(equity_data),
            "max_drawdown_pct": self._calculate_max_drawdown_pct(equity_data),
            "var_95": np.percentile(returns, 5) if len(returns) > 0 else 0.0,  # 95% VaR
            "var_99": np.percentile(returns, 1) if len(returns) > 0 else 0.0,  # 99% VaR
            "volatility": returns.std() * np.sqrt(252) if len(returns) > 0 else 0.0,
        }

    def _get_position_analysis(self):
        """获取持仓分析"""
        trades = self._get_closed_trades()

        if not trades:
            return {
                "avg_holding_period": 0.0,
                "max_holding_period": 0.0,
                "min_holding_period": 0.0,
                "long_trades": 0,
                "short_trades": 0,
            }

        holding_periods = []
        long_trades = 0
        short_trades = 0

        for trade in trades:
            # 计算持仓时间（小时）
            if (
                hasattr(trade, "dtopen")
                and hasattr(trade, "dtclose")
                and trade.dtopen
                and trade.dtclose
            ):
                start_dt = bt.num2date(trade.dtopen)
                end_dt = bt.num2date(trade.dtclose)
                hold_hours = (end_dt - start_dt).total_seconds() / 3600
                holding_periods.append(hold_hours)

            # 统计多空交易数量
            if hasattr(trade, "size"):
                if trade.size > 0:
                    long_trades += 1
                elif trade.size < 0:
                    short_trades += 1

        return {
            "avg_holding_period": np.mean(holding_periods) if holding_periods else 0.0,
            "max_holding_period": max(holding_periods) if holding_periods else 0.0,
            "min_holding_period": min(holding_periods) if holding_periods else 0.0,
            "long_trades": long_trades,
            "short_trades": short_trades,
        }

    def _get_time_analysis(self):
        """获取时间分析"""
        return {
            "start_time": self._get_start_date(),
            "end_time": self._get_end_date(),
            "trading_days": self._calculate_trading_days(),
            "bars_per_day": self._calculate_bars_per_day(),
        }

    def _get_detailed_trades(self):
        """获取详细交易记录"""
        trades = self._get_closed_trades()

        trade_details = []
        for trade in trades:
            # 计算持仓时间
            holding_period_hours = 0
            if (
                hasattr(trade, "dtopen")
                and hasattr(trade, "dtclose")
                and trade.dtopen
                and trade.dtclose
            ):
                start_dt = bt.num2date(trade.dtopen)
                end_dt = bt.num2date(trade.dtclose)
                holding_period_hours = (end_dt - start_dt).total_seconds() / 3600

            trade_info = {
                "entry_time": (
                    bt.num2date(trade.dtopen).strftime("%Y-%m-%d %H:%M:%S")
                    if hasattr(trade, "dtopen") and trade.dtopen
                    else None
                ),
                "exit_time": (
                    bt.num2date(trade.dtclose).strftime("%Y-%m-%d %H:%M:%S")
                    if hasattr(trade, "dtclose") and trade.dtclose
                    else None
                ),
                "entry_price": getattr(trade, "price", 0),
                "exit_price": getattr(trade, "close_price", 0),
                "size": getattr(trade, "size", 0),
                "pnl": getattr(trade, "pnl", 0),
                "commission": getattr(trade, "commission", 0),
                "holding_period_hours": holding_period_hours,
            }
            trade_details.append(trade_info)

        return trade_details

    def _get_equity_curve(self):
        """获取资金曲线数据"""
        equity_data = self._get_equity_data()

        if equity_data is None:
            return []

        # 转换为列表格式，便于JSON序列化
        curve_data = []
        for i, value in enumerate(equity_data):
            curve_data.append(
                {
                    "index": i,
                    "value": float(value),
                    "timestamp": self._get_timestamp_for_index(i),
                }
            )

        return curve_data

    def _generate_summary(self, report):
        """生成报告摘要"""
        perf = report["performance_metrics"]
        trade = report["trade_analysis"]
        risk = report["risk_metrics"]

        summary = f"""
策略统计报告摘要
================

基本信息:
- 策略名称: {report['basic_info']['strategy_name']}
- 交易品种: {report['basic_info']['symbol']}
- 回测期间: {report['basic_info']['start_date']} 至 {report['basic_info']['end_date']}
- 初始资金: {report['basic_info']['initial_cash']:,.2f}
- 最终价值: {report['basic_info']['final_value']:,.2f}

性能指标:
- 总收益率: {perf.get('total_return_pct', 0):.2f}%
- 总盈亏: {perf.get('total_pnl', 0):,.2f}
- 夏普比率: {perf.get('sharpe_ratio', 0):.3f}
- 最大回撤: {risk.get('max_drawdown_pct', 0):.2f}%
- 年化波动率: {risk.get('volatility', 0):.2f}%

交易统计:
- 总交易次数: {trade['total_trades']}
- 胜率: {trade['win_rate_pct']:.2f}%
- 盈利因子: {trade['profit_factor']:.2f}
- 平均盈利: {trade['avg_win']:,.2f}
- 平均亏损: {trade['avg_loss']:,.2f}
- 最大单笔盈利: {trade['largest_win']:,.2f}
- 最大单笔亏损: {trade['largest_loss']:,.2f}
"""
        return summary.strip()

    # 辅助方法
    def _get_closed_trades(self):
        """获取已关闭的交易"""
        if hasattr(self.strategy, "_trades") and self.data in self.strategy._trades:
            return [
                trade for trade in self.strategy._trades[self.data][0] if trade.isclosed
            ]
        return []

    def _get_equity_data(self):
        """获取资金曲线数据"""
        # 尝试从broker的observers获取资金数据
        if hasattr(self.strategy, "observers"):
            for observer in self.strategy.observers:
                if hasattr(observer, "lines") and hasattr(observer.lines, "value"):
                    # 找到Broker observer
                    equity_array = observer.lines.value.array
                    if len(equity_array) > 0:
                        return pd.Series(equity_array)

        # 如果没有找到，返回None
        return None

    def _get_start_date(self):
        """获取开始日期"""
        if len(self.data.datetime.array) > 0:
            return bt.num2date(self.data.datetime.array[0]).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
        return "Unknown"

    def _get_end_date(self):
        """获取结束日期"""
        if len(self.data.datetime.array) > 0:
            return bt.num2date(self.data.datetime.array[-1]).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
        return "Unknown"

    def _get_commission_rate(self):
        """获取手续费率"""
        if hasattr(self.broker, "p") and hasattr(self.broker.p, "commission"):
            commission = self.broker.p.commission
            if hasattr(commission, "commission"):
                return commission.commission
        return 0.0

    def _get_timestamp_for_index(self, index):
        """获取指定索引的时间戳"""
        if index < len(self.data.datetime.array):
            return bt.num2date(self.data.datetime.array[index]).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
        return None

    def _calculate_trading_days(self):
        """计算交易天数"""
        if len(self.data.datetime.array) > 1:
            start = bt.num2date(self.data.datetime.array[0])
            end = bt.num2date(self.data.datetime.array[-1])
            return (end - start).days
        return 0

    def _calculate_bars_per_day(self):
        """计算每日K线数量"""
        trading_days = self._calculate_trading_days()
        if trading_days > 0:
            return len(self.data.datetime.array) / trading_days
        return 0

    def _calculate_sharpe_ratio(self, returns, risk_free_rate=0.02):
        """计算夏普比率"""
        if len(returns) == 0:
            return 0.0

        excess_returns = returns - risk_free_rate / 252  # 日无风险收益率
        if returns.std() == 0:
            return 0.0

        return excess_returns.mean() / returns.std() * np.sqrt(252)

    def _calculate_max_drawdown(self, equity_data):
        """计算最大回撤（绝对值）"""
        if equity_data is None or len(equity_data) <= 1:
            return 0.0

        peak = equity_data.expanding().max()
        drawdown = peak - equity_data
        return drawdown.max()

    def _calculate_max_drawdown_pct(self, equity_data):
        """计算最大回撤百分比"""
        if equity_data is None or len(equity_data) <= 1:
            return 0.0

        peak = equity_data.expanding().max()
        drawdown_pct = (peak - equity_data) / peak * 100
        return drawdown_pct.max()

    def _calculate_calmar_ratio(self, total_return, equity_data):
        """计算卡尔玛比率"""
        max_dd_pct = self._calculate_max_drawdown_pct(equity_data)
        if max_dd_pct == 0:
            return float("inf") if total_return > 0 else 0.0

        # 年化收益率 / 最大回撤百分比
        trading_days = self._calculate_trading_days()
        if trading_days > 0:
            annualized_return = (1 + total_return) ** (365 / trading_days) - 1
            return annualized_return * 100 / max_dd_pct

        return 0.0
