#!/usr/bin/env python3
"""
测试持仓分析修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bt2.trade import EventTrade
from bt2.analyzers.report import StrategyReportGenerator
import backtrader as bt
from datetime import datetime, timedelta

# 创建一个模拟的策略对象用于测试
class MockStrategy:
    def __init__(self):
        self.broker = MockBroker()
        self.data = MockData()
        self._trades = {self.data: [[]]}
        
        # 创建一些模拟交易
        self._create_mock_trades()
    
    def _create_mock_trades(self):
        """创建模拟交易数据"""
        trades = []
        
        # 创建几个已关闭的交易
        for i in range(3):
            trade = EventTrade()
            trade.size = 100 if i % 2 == 0 else -100  # 交替多空
            trade.price = 50000 + i * 100
            trade.close_price = 50000 + i * 100 + (50 if i % 2 == 0 else -50)
            trade.pnl = (50 if i % 2 == 0 else -50) * abs(trade.size)
            trade.commission = 10
            trade.isclosed = True
            trade.isopen = False
            
            # 设置开仓和平仓时间
            base_time = datetime(2025, 1, 1, 10, 0, 0)
            trade.dtopen = bt.date2num(base_time + timedelta(hours=i))
            trade.dtclose = bt.date2num(base_time + timedelta(hours=i+2))  # 持仓2小时
            
            trades.append(trade)
        
        self._trades[self.data][0] = trades

class MockBroker:
    def __init__(self):
        self.p = type('P', (), {'cashbalance': 100000})()
    
    def getvalue(self):
        return 105000

class MockData:
    def __init__(self):
        self.datetime = type('DT', (), {'array': [bt.date2num(datetime.now())]})()

def test_position_analysis():
    """测试持仓分析修复"""
    print("测试持仓分析修复...")
    
    # 创建模拟策略
    strategy = MockStrategy()
    
    # 创建报告生成器
    generator = StrategyReportGenerator(strategy)
    
    # 测试持仓分析
    position_analysis = generator._get_position_analysis()
    
    print("持仓分析结果:")
    for key, value in position_analysis.items():
        print(f"  {key}: {value}")
    
    # 验证结果
    assert position_analysis['long_trades'] == 2, f"多头交易数量错误: {position_analysis['long_trades']}"
    assert position_analysis['short_trades'] == 1, f"空头交易数量错误: {position_analysis['short_trades']}"
    assert position_analysis['avg_holding_period'] == 2.0, f"平均持仓时间错误: {position_analysis['avg_holding_period']}"
    assert position_analysis['max_holding_period'] == 2.0, f"最大持仓时间错误: {position_analysis['max_holding_period']}"
    assert position_analysis['min_holding_period'] == 2.0, f"最小持仓时间错误: {position_analysis['min_holding_period']}"
    
    print("✓ 持仓分析测试通过!")
    
    # 测试详细交易记录
    detailed_trades = generator._get_detailed_trades()
    
    print(f"\n详细交易记录 ({len(detailed_trades)} 条):")
    for i, trade in enumerate(detailed_trades):
        print(f"  交易 {i+1}:")
        for key, value in trade.items():
            print(f"    {key}: {value}")
    
    # 验证详细交易记录
    assert len(detailed_trades) == 3, f"交易记录数量错误: {len(detailed_trades)}"
    for trade in detailed_trades:
        assert trade['holding_period_hours'] == 2.0, f"持仓时间错误: {trade['holding_period_hours']}"
        assert trade['entry_time'] is not None, "开仓时间不能为空"
        assert trade['exit_time'] is not None, "平仓时间不能为空"
    
    print("✓ 详细交易记录测试通过!")

if __name__ == "__main__":
    test_position_analysis()
    print("\n所有测试通过! 持仓分析修复成功。")
